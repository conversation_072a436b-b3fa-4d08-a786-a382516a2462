#!/bin/bash

# 检查会话是否已存在，如果存在则连接，否则创建新会话
if tmux has-session -t lerobot_session 2>/dev/null; then
    echo "lerobot_session 已存在，连接到现有会话..."
    tmux attach-session -t lerobot_session
    exit 0
fi

# 启动 tmux 会话并正确配置 lerobot 环境
tmux new-session -d -s lerobot_session -c /root/workspace/lerobot

# 在 tmux 会话中设置环境
tmux send-keys -t lerobot_session 'conda activate lerobot' Enter
tmux send-keys -t lerobot_session 'export PATH="/root/miniconda3/envs/lerobot/bin:$PATH"' Enter
tmux send-keys -t lerobot_session 'alias python="/root/miniconda3/envs/lerobot/bin/python"' Enter
tmux send-keys -t lerobot_session 'alias pip="/root/miniconda3/envs/lerobot/bin/pip"' Enter
tmux send-keys -t lerobot_session 'echo "Python 版本: $(python --version)"' Enter
tmux send-keys -t lerobot_session 'echo "Python 路径: $(which python)"' Enter
tmux send-keys -t lerobot_session 'echo "lerobot 环境已正确配置！"' Enter

# 连接到会话
tmux attach-session -t lerobot_session
